# from flask import Flask, request, jsonify
# from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room
# from flask_cors import CORS
# import sys
# import os
# import json
# import uuid
# from datetime import datetime, timezone, timedelta
# import base64
# import asyncio
# import threading

# # LiveKit imports
# from livekit import api, rtc
# from livekit.api import AccessToken, VideoGrants, LiveKitAPI
# from livekit.rtc import Room, TrackSource

# # Add parent directory to path to import shared modules
# sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# from dotenv import load_dotenv
# from shared.database import Database  # Import Database

# # Import ProxyFix for handling reverse proxy headers
# from werkzeug.middleware.proxy_fix import ProxyFix

# load_dotenv()

# app = Flask(__name__)
# app.config['SECRET_KEY'] = 'enhanced-streaming-secret-key'

# # Configure CORS to allow all origins for development
# CORS(app,
#      origins="*",
#      allow_headers=["Content-Type", "Authorization", "Access-Control-Allow-Credentials"],
#      supports_credentials=True,
#      methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"])

# # Apply ProxyFix middleware to handle Nginx proxy headers
# app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_port=1, x_prefix=1)

# # Initialize Socket.IO with CORS support for all origins
# # async_mode will be auto-detected (eventlet in production, threading in development)
# socketio = SocketIO(app,
#                    cors_allowed_origins="*",
#                    cors_credentials=True,
#                    logger=True,
#                    engineio_logger=True)

# # Initialize Database
# db = Database()

# # Stream permissions table is created in database.py with proper PostgreSQL syntax

# # Add CORS preflight handler
# @app.before_request
# def handle_preflight():
#     if request.method == "OPTIONS":
#         response = jsonify({'status': 'OK'})
#         response.headers.add("Access-Control-Allow-Origin", "*")
#         response.headers.add('Access-Control-Allow-Headers', "*")
#         response.headers.add('Access-Control-Allow-Methods', "*")
#         return response

# # LiveKit Configuration from environment variables
# LIVEKIT_URL = os.getenv('LIVEKIT_URL')
# LIVEKIT_API_KEY = os.getenv('LIVEKIT_API_KEY')
# LIVEKIT_API_SECRET = os.getenv('LIVEKIT_API_SECRET')

# # Initialize LiveKit API Client - Lazy Initialization
# livekit_api = None

# def get_livekit_api():
#     """Get or create LiveKit API client"""
#     global livekit_api
#     if livekit_api is None:
#         livekit_api = LiveKitAPI(LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
#     return livekit_api

# # Global storage for enhanced streaming features
# enhanced_streams = {}
# stream_recordings = {}
# chat_messages = {}
# quality_settings = {}
# livekit_rooms = {}  # Track LiveKit rooms

# class LiveKitManager:
#     """LiveKit integration manager for room and token management"""

#     def __init__(self):
#         self.rooms = {}

#     def generate_access_token(self, room_name, participant_identity, participant_name=None, is_teacher=False):
#         """Generate LiveKit access token for a participant"""
#         try:
#             token = AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
#             token.with_identity(participant_identity)
#             token.with_name(participant_name or participant_identity)

#             grants = VideoGrants(
#                 room_join=True,
#                 room=room_name,
#                 can_publish=is_teacher,
#                 can_subscribe=True,
#                 can_publish_data=True,
#                 can_update_own_metadata=True
#             )

#             if is_teacher:
#                 grants.room_admin = True

#             token.with_grants(grants)
#             token.with_ttl(timedelta(hours=24))
#             jwt_token = token.to_jwt()
#             print(f"✅ Generated LiveKit token for {participant_identity} in room {room_name}")
#             return jwt_token
#         except Exception as e:
#             print(f"❌ Error generating LiveKit token: {e}")
#             return None

#     async def create_room(self, room_name, max_participants=50):
#         """Create a LiveKit room"""
#         try:
#             room_request = api.CreateRoomRequest(
#                 name=room_name,
#                 max_participants=max_participants,
#                 empty_timeout=10 * 60,
#                 departure_timeout=60
#             )
#             api_client = get_livekit_api()
#             room = await api_client.room.create_room(room_request)
#             self.rooms[room_name] = {
#                 'room': room,
#                 'created_at': datetime.now(timezone.utc),
#                 'participants': [],
#                 'max_participants': max_participants
#             }
#             print(f"✅ Created LiveKit room: {room_name}")
#             return room
#         except Exception as e:
#             print(f"❌ Error creating LiveKit room {room_name}: {e}")
#             return None

#     async def delete_room(self, room_name):
#         """Delete a LiveKit room"""
#         try:
#             api_client = get_livekit_api()
#             await api_client.room.delete_room(api.DeleteRoomRequest(room=room_name))
#             if room_name in self.rooms:
#                 del self.rooms[room_name]
#             print(f"✅ Deleted LiveKit room: {room_name}")
#             return True
#         except Exception as e:
#             print(f"❌ Error deleting LiveKit room {room_name}: {e}")
#             return False

#     async def list_participants(self, room_name):
#         """List participants in a LiveKit room"""
#         try:
#             api_client = get_livekit_api()
#             response = await api_client.room.list_participants(api.ListParticipantsRequest(room=room_name))
#             return response.participants
#         except Exception as e:
#             print(f"❌ Error listing participants in room {room_name}: {e}")
#             return []

#     async def remove_participant(self, room_name, participant_identity):
#         """Remove a participant from a LiveKit room"""
#         try:
#             api_client = get_livekit_api()
#             await api_client.room.remove_participant(api.RoomParticipantIdentity(
#                 room=room_name,
#                 identity=participant_identity
#             ))
#             print(f"✅ Removed participant {participant_identity} from room {room_name}")
#             return True
#         except Exception as e:
#             print(f"❌ Error removing participant {participant_identity} from room {room_name}: {e}")
#             return False

# # Initialize LiveKit Manager
# livekit_manager = LiveKitManager()

# class EnhancedStreamManager:
#     def __init__(self):
#         self.streams = {}
#         self.connections = {}
#         self.recordings = {}

#     def create_enhanced_stream(self, teacher_id, session_id, socket_id, quality='medium'):
#         """Create an enhanced streaming session with LiveKit integration"""
#         stream_data = {
#             'teacher_id': teacher_id,
#             'session_id': session_id,
#             'teacher_socket': socket_id,
#             'viewers': [],
#             'created_at': datetime.now(timezone.utc),
#             'status': 'active',
#             'quality': quality,
#             'recording_enabled': True,
#             'chat_enabled': True,
#             'screen_sharing': True,
#             'viewer_count': 0,
#             'livekit_room': session_id,
#             'livekit_enabled': True
#         }
#         self.streams[session_id] = stream_data
#         chat_messages[session_id] = []
#         quality_settings[session_id] = {
#             'video_quality': quality,
#             'frame_rate': 30 if quality == 'high' else 20,
#             'resolution': '1920x1080' if quality == 'high' else '1280x720'
#         }

#         def create_livekit_room():
#             loop = asyncio.new_event_loop()
#             asyncio.set_event_loop(loop)
#             try:
#                 loop.run_until_complete(livekit_manager.create_room(session_id))
#             except Exception as e:
#                 print(f"❌ Error creating LiveKit room: {e}")
#             finally:
#                 loop.close()

#         threading.Thread(target=create_livekit_room, daemon=True).start()
#         print(f"✅ Enhanced stream with LiveKit created: {session_id} by teacher {teacher_id}")
#         return stream_data

#     def add_viewer(self, session_id, viewer_id, socket_id):
#         """Add a viewer to the enhanced stream"""
#         if session_id in self.streams:
#             viewer_data = {
#                 'viewer_id': viewer_id,
#                 'socket_id': socket_id,
#                 'joined_at': datetime.now(timezone.utc)
#             }
#             self.streams[session_id]['viewers'].append(viewer_data)
#             self.streams[session_id]['viewer_count'] = len(self.streams[session_id]['viewers'])
#             return True
#         return False

#     def remove_viewer(self, session_id, socket_id):
#         """Remove a viewer from the enhanced stream"""
#         if session_id in self.streams:
#             self.streams[session_id]['viewers'] = [
#                 v for v in self.streams[session_id]['viewers']
#                 if v['socket_id'] != socket_id
#             ]
#             self.streams[session_id]['viewer_count'] = len(self.streams[session_id]['viewers'])
#             return True
#         return False

#     def get_stream(self, session_id):
#         """Get enhanced stream information"""
#         return self.streams.get(session_id)

#     def stop_stream(self, session_id):
#         """Stop enhanced stream and save recording"""
#         print(f"🛑 Attempting to stop stream: {session_id}")
#         if session_id in self.streams:
#             stream = self.streams[session_id]
#             print(f"📊 Stream found - Teacher: {stream.get('teacher_id')}, Status: {stream.get('status')}, Viewers: {stream.get('viewer_count', 0)}")
#             stream['status'] = 'stopped'
#             stream['ended_at'] = datetime.now(timezone.utc)

#             if stream.get('recording_enabled'):
#                 duration = (datetime.now(timezone.utc) - stream['created_at']).total_seconds()
#                 self.recordings[session_id] = {
#                     'session_id': session_id,
#                     'teacher_id': stream['teacher_id'],
#                     'duration': duration,
#                     'quality': stream['quality'],
#                     'viewer_count': stream['viewer_count'],
#                     'chat_messages': len(chat_messages.get(session_id, [])),
#                     'recorded_at': datetime.now(timezone.utc)
#                 }
#                 print(f"💾 Recording saved - Duration: {duration:.1f}s, Quality: {stream['quality']}")

#             if stream.get('livekit_enabled'):
#                 def delete_livekit_room():
#                     loop = asyncio.new_event_loop()
#                     asyncio.set_event_loop(loop)
#                     try:
#                         loop.run_until_complete(livekit_manager.delete_room(session_id))
#                     except Exception as e:
#                         print(f"❌ Error deleting LiveKit room: {e}")
#                     finally:
#                         loop.close()
#                 threading.Thread(target=delete_livekit_room, daemon=True).start()

#             if session_id in chat_messages:
#                 del chat_messages[session_id]
#                 print(f"🧹 Cleaned up chat messages for session {session_id}")
#             if session_id in quality_settings:
#                 del quality_settings[session_id]
#                 print(f"🧹 Cleaned up quality settings for session {session_id}")

#             del self.streams[session_id]
#             print(f"✅ Stream {session_id} stopped and cleaned up successfully")
#             return True
#         else:
#             print(f"❌ Stream {session_id} not found in active streams")
#             print(f"📋 Active streams: {list(self.streams.keys())}")
#             return False

# enhanced_stream_manager = EnhancedStreamManager()

# def get_user_role_by_id(user_id):
#     """Helper function to get user role by ID from various tables."""
#     # Check users table first
#     user = db.execute_query_one("SELECT role FROM users WHERE id = %s", (user_id,))
#     if user:
#         return user['role']

#     # Check centers table for center counselors
#     center = db.execute_query_one("SELECT 'center_counselor' as role FROM centers WHERE center_code = %s", (user_id,))
#     if center:
#         return 'center_counselor'

#     # Check students table
#     student = db.execute_query_one("SELECT 'student' as role FROM students WHERE id = %s", (user_id,))
#     if student:
#         return 'student'

#     # Check parents table
#     parent = db.execute_query_one("SELECT 'parent' as role FROM parents WHERE id = %s", (user_id,))
#     if parent:
#         return 'parent'

#     # Check faculty table - center trainee faculty should be here
#     faculty = db.execute_query_one("SELECT 'faculty' as role FROM faculty WHERE id = %s", (user_id,))
#     if faculty:
#         return 'faculty'

#     # Check for kota teachers
#     kota_teacher = db.execute_query_one("SELECT 'kota_teacher' as role FROM users WHERE id = %s AND role = 'kota_teacher'", (user_id,))
#     if kota_teacher:
#         return 'kota_teacher'
#     teacher = db.execute_query_one("SELECT 'kota_teacher' as role FROM kota_teachers WHERE id = %s", (user_id,))
#     if teacher:
#         return teacher['role']
#     return None

# @app.route('/health', methods=['GET'])
# def health_check():
#     """Enhanced streaming service health check with LiveKit status"""
#     livekit_status = 'configured' if all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]) else 'not configured'
#     return jsonify({
#         'status': 'healthy',
#         'service': 'Enhanced Real-time Streaming Service with LiveKit',
#         'port': 8012,  # Updated to match the running port
#         'features': ['LiveKit', 'WebRTC', 'Socket.IO', 'Chat', 'Recording', 'Quality Controls'],
#         'active_streams': len(enhanced_stream_manager.streams),
#         'livekit': {
#             'status': livekit_status,
#             'url': LIVEKIT_URL,
#             'api_key': LIVEKIT_API_KEY[:8] + '...' if LIVEKIT_API_KEY else None,
#             'rooms_managed': len(livekit_manager.rooms)
#         },
#         'timestamp': datetime.now().isoformat()
#     }), 200

# # HTTP-based Chat API Endpoints
# @app.route('/api/chat/send', methods=['POST'])
# def send_chat_message():
#     """Send a chat message via HTTP"""
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id')
#         message = data.get('message')
#         sender_id = data.get('sender_id')
#         sender_name = data.get('sender_name', 'Anonymous')

#         if not session_id or not message:
#             return jsonify({'message': 'session_id and message are required'}), 400

#         # Get sender role
#         sender_role = get_user_role_by_id(sender_id) or 'unknown'

#         # Initialize chat messages for session if not exists
#         if session_id not in chat_messages:
#             chat_messages[session_id] = []

#         # Create chat message
#         chat_data = {
#             'id': str(uuid.uuid4()),
#             'session_id': session_id,
#             'sender_id': sender_id,
#             'sender_name': sender_name,
#             'sender_role': sender_role,
#             'message': message,
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }

#         # Store message
#         chat_messages[session_id].append(chat_data)

#         # Also emit via Socket.IO for real-time updates (if anyone is connected)
#         socketio.emit('chat_message', chat_data, room=session_id)

#         print(f"💬 HTTP Chat message from {sender_name} ({sender_role}) in session {session_id}: {message}")

#         return jsonify({
#             'success': True,
#             'message': 'Message sent successfully',
#             'chat_data': chat_data
#         }), 200

#     except Exception as e:
#         print(f"❌ Error sending chat message via HTTP: {e}")
#         return jsonify({'message': 'Failed to send message', 'error': str(e)}), 500

# @app.route('/api/chat/history/<session_id>', methods=['GET'])
# def get_chat_history(session_id):
#     """Get chat history for a session via HTTP"""
#     try:
#         if session_id and session_id in chat_messages:
#             history = chat_messages[session_id][-50:]  # Last 50 messages
#             return jsonify({
#                 'success': True,
#                 'session_id': session_id,
#                 'messages': history,
#                 'total_messages': len(history)
#             }), 200
#         else:
#             return jsonify({
#                 'success': True,
#                 'session_id': session_id,
#                 'messages': [],
#                 'total_messages': 0
#             }), 200

#     except Exception as e:
#         print(f"❌ Error getting chat history via HTTP: {e}")
#         return jsonify({'message': 'Failed to get chat history', 'error': str(e)}), 500

# @app.route('/api/enhanced-stream/start', methods=['POST'])
# def start_enhanced_stream():
#     """Start enhanced streaming session with LiveKit"""
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id', str(uuid.uuid4()))
#         quality = data.get('quality', 'medium')
#         teacher_id = data.get('teacher_id', 'demo_teacher')
#         teacher_name = data.get('teacher_name', teacher_id)

#         stream = enhanced_stream_manager.create_enhanced_stream(
#             teacher_id, session_id, None, quality
#         )

#         teacher_token = livekit_manager.generate_access_token(
#             room_name=session_id,
#             participant_identity=teacher_id,
#             participant_name=teacher_name,
#             is_teacher=True
#         )

#         # Dynamically generate WebSocket URL
#         ws_scheme = 'wss' if request.scheme == 'https' else 'ws'
#         stream_url = f"{ws_scheme}://{request.host}/socket.io/"

#         return jsonify({
#             'message': 'Enhanced stream with LiveKit started successfully',
#             'session_id': session_id,
#             'stream_url': stream_url,
#             'livekit_url': LIVEKIT_URL,
#             'livekit_token': teacher_token,
#             'features': {
#                 'chat': True,
#                 'recording': True,
#                 'quality_controls': True,
#                 'screen_sharing': True,
#                 'livekit_enabled': True
#             },
#             'quality_settings': quality_settings[session_id]
#         }), 200
#     except Exception as e:
#         print(f"Enhanced stream start error: {e}")
#         return jsonify({'message': 'Failed to start enhanced stream'}), 500

# @app.route('/api/livekit/token', methods=['POST'])
# def generate_livekit_token():
#     """Generate LiveKit access token for participants"""
#     try:
#         data = request.get_json()
#         room_name = data.get('room_name') or data.get('session_id')
#         participant_id = data.get('participant_id') or data.get('user_id')
#         participant_name = data.get('participant_name', participant_id)
#         is_teacher = data.get('is_teacher', False)

#         if not room_name or not participant_id:
#             return jsonify({'message': 'room_name and participant_id are required'}), 400

#         stream = enhanced_stream_manager.get_stream(room_name)
#         if not stream:
#             return jsonify({'message': 'Stream not found'}), 404

#         token = livekit_manager.generate_access_token(
#             room_name=room_name,
#             participant_identity=participant_id,
#             participant_name=participant_name,
#             is_teacher=is_teacher
#         )

#         if token:
#             return jsonify({
#                 'token': token,
#                 'livekit_url': LIVEKIT_URL,
#                 'room_name': room_name,
#                 'participant_id': participant_id,
#                 'is_teacher': is_teacher
#             }), 200
#         else:
#             return jsonify({'message': 'Failed to generate token'}), 500
#     except Exception as e:
#         print(f"Token generation error: {e}")
#         return jsonify({'message': 'Failed to generate token'}), 500

# @app.route('/api/livekit/join', methods=['POST'])
# def join_livekit_room():
#     """Join LiveKit room and get connection details"""
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id')
#         user_id = data.get('user_id')
#         user_name = data.get('user_name', user_id)
#         user_role = data.get('user_role', 'student')

#         if not session_id or not user_id:
#             return jsonify({'message': 'session_id and user_id are required'}), 400

#         stream = enhanced_stream_manager.get_stream(session_id)
#         if not stream:
#             return jsonify({'message': 'Stream not found'}), 404

#         # NEW: Check stream permission ONLY for students (not faculty/teachers)
#         user_role_from_db = get_user_role_by_id(user_id) or user_role
#         print(f"🔍 User {user_id} role check: provided='{user_role}', from_db='{user_role_from_db}'")

#         # Define roles that don't need permission (faculty, teachers, counselors)
#         privileged_roles = ['faculty', 'kota_teacher', 'center_counselor', 'teacher']

#         # Only students need permission, all other roles can join directly
#         if user_role_from_db == 'student':
#             permission = db.execute_query_one(
#                 "SELECT status FROM stream_permissions WHERE student_id = %s AND session_id = %s AND status = 'approved' ORDER BY requested_at DESC LIMIT 1",
#                 (user_id, session_id)
#             )
#             if not permission:
#                 return jsonify({
#                     'message': 'Permission required to join this stream. Please request permission from your faculty.',
#                     'permission_required': True,
#                     'user_role': user_role_from_db
#                 }), 403
#             print(f"✅ Student {user_id} has permission to join stream {session_id}")
#         elif user_role_from_db in privileged_roles or user_role in privileged_roles:
#             print(f"✅ {user_role_from_db or user_role} {user_id} can join stream {session_id} without permission")
#         else:
#             # For unknown roles, allow join but log it
#             print(f"⚠️ Unknown role '{user_role_from_db or user_role}' for user {user_id}, allowing join")

#         is_teacher = (user_role in ['faculty', 'kota_teacher'] or user_id == stream.get('teacher_id'))
#         token = livekit_manager.generate_access_token(
#             room_name=session_id,
#             participant_identity=user_id,
#             participant_name=user_name,
#             is_teacher=is_teacher
#         )

#         if token:
#             return jsonify({
#                 'success': True,
#                 'livekit_url': LIVEKIT_URL,
#                 'token': token,
#                 'room_name': session_id,
#                 'participant_id': user_id,
#                 'participant_name': user_name,
#                 'is_teacher': is_teacher,
#                 'stream_info': {
#                     'session_id': session_id,
#                     'teacher_id': stream['teacher_id'],
#                     'viewer_count': stream['viewer_count'],
#                     'quality': stream['quality'],
#                     'features': {
#                         'chat_enabled': stream['chat_enabled'],
#                         'recording_enabled': stream['recording_enabled'],
#                         'screen_sharing': stream['screen_sharing']
#                     }
#                 }
#             }), 200
#         else:
#             return jsonify({'message': 'Failed to generate access token'}), 500
#     except Exception as e:
#         print(f"LiveKit join error: {e}")
#         return jsonify({'message': 'Failed to join LiveKit room'}), 500

# @app.route('/api/enhanced-stream/stop', methods=['POST'])
# def stop_enhanced_stream():
#     """Stop enhanced streaming session"""
#     try:
#         data = request.get_json() or {}
#         session_id = data.get('session_id')
#         teacher_id = data.get('teacher_id')

#         print(f"🛑 Received stop request for session: {session_id}, teacher: {teacher_id}")
#         if not session_id and teacher_id:
#             for sid, stream in enhanced_stream_manager.streams.items():
#                 if stream.get('teacher_id') == teacher_id and stream.get('status') == 'active':
#                     session_id = sid
#                     print(f"🔍 Found active stream for teacher {teacher_id}: {session_id}")
#                     break

#         if not session_id:
#             active_sessions = list(enhanced_stream_manager.streams.keys())
#             if active_sessions:
#                 print(f"⚠️ No session_id provided, stopping all active streams: {active_sessions}")
#                 stopped_count = 0
#                 for sid in active_sessions:
#                     if enhanced_stream_manager.stop_stream(sid):
#                         socketio.emit('stream_ended', {
#                             'session_id': sid,
#                             'message': 'Stream has ended'
#                         }, room=sid)
#                         stopped_count += 1
#                 print(f"✅ Stopped {stopped_count} streams and notified all viewers")
#                 return jsonify({
#                     'message': f'Stopped {stopped_count} active streams',
#                     'stopped_sessions': active_sessions,
#                     'recording_saved': True
#                 }), 200
#             else:
#                 return jsonify({'message': 'No active streams found'}), 404

#         success = enhanced_stream_manager.stop_stream(session_id)
#         if success:
#             socketio.emit('stream_ended', {
#                 'session_id': session_id,
#                 'message': 'Stream has ended'
#             }, room=session_id)
#             print(f"✅ Successfully stopped stream {session_id} and notified viewers")
#             return jsonify({
#                 'message': 'Enhanced stream stopped successfully',
#                 'session_id': session_id,
#                 'recording_saved': True
#             }), 200
#         else:
#             print(f"❌ Stream {session_id} not found")
#             return jsonify({'message': 'Stream not found'}), 404
#     except Exception as e:
#         print(f"❌ Enhanced stream stop error: {e}")
#         return jsonify({'message': 'Failed to stop enhanced stream', 'error': str(e)}), 500

# @app.route('/api/enhanced-stream/status/<session_id>', methods=['GET'])
# def get_enhanced_stream_status(session_id):
#     """Get enhanced stream status"""
#     stream = enhanced_stream_manager.get_stream(session_id)
#     if stream:
#         return jsonify({
#             'session_id': session_id,
#             'status': stream['status'],
#             'viewer_count': stream['viewer_count'],
#             'quality': stream['quality'],
#             'features': {
#                 'chat_enabled': stream['chat_enabled'],
#                 'recording_enabled': stream['recording_enabled'],
#                 'screen_sharing': stream['screen_sharing']
#             },
#             'uptime': (datetime.now(timezone.utc) - stream['created_at']).total_seconds()
#         }), 200
#     else:
#         return jsonify({'message': 'Stream not found'}), 404

# @app.route('/active-streams', methods=['GET'])
# def get_active_streams():
#     """Get all active enhanced streams"""
#     try:
#         active_streams = []
#         for session_id, stream in enhanced_stream_manager.streams.items():
#             if stream['status'] == 'active':
#                 # Get teacher name from database
#                 teacher_info = db.execute_query_one(
#                     "SELECT COALESCE(first_name || ' ' || last_name, username) as name FROM kota_teachers WHERE id = %s",
#                     (stream['teacher_id'],)
#                 )
#                 teacher_name = teacher_info['name'] if teacher_info else stream['teacher_id']

#                 stream_info = {
#                     'session_id': session_id,
#                     'teacher_id': stream['teacher_id'],
#                     'teacher_name': teacher_name,
#                     'viewer_count': stream['viewer_count'],
#                     'quality': stream['quality'],
#                     'created_at': stream['created_at'].isoformat(),
#                     'uptime': (datetime.now(timezone.utc) - stream['created_at']).total_seconds(),
#                     'features': {
#                         'chat_enabled': stream['chat_enabled'],
#                         'recording_enabled': stream['recording_enabled'],
#                         'screen_sharing': stream['screen_sharing']
#                     }
#                 }
#                 active_streams.append(stream_info)
#         return jsonify({
#             'success': True,
#             'streams': active_streams,
#             'active_streams': active_streams,
#             'total_count': len(active_streams),
#             'service': 'Enhanced Real-time Streaming',
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }), 200
#     except Exception as e:
#         print(f"❌ Error getting active streams: {e}")
#         return jsonify({'message': 'Failed to get active streams', 'error': str(e)}), 500

# # NEW STREAM PERMISSION ENDPOINTS

# @app.route('/api/stream-permission-request', methods=['POST'])
# def request_stream_permission():
#     """Student requests permission to join a live stream"""
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id')
#         student_id = data.get('student_id')
#         faculty_id = data.get('faculty_id')
#         message = data.get('message', 'Please allow me to join the live stream')

#         if not all([session_id, student_id, faculty_id]):
#             return jsonify({'message': 'session_id, student_id, and faculty_id are required'}), 400

#         # Check if stream exists
#         stream = enhanced_stream_manager.get_stream(session_id)
#         if not stream:
#             return jsonify({'message': 'Stream not found'}), 404

#         # Check if there's already a pending or approved request for THIS specific stream
#         existing_request = db.execute_query_one(
#             "SELECT id, status FROM stream_permissions WHERE student_id = %s AND session_id = %s AND status IN ('pending', 'approved') ORDER BY requested_at DESC LIMIT 1",
#             (student_id, session_id)
#         )

#         if existing_request:
#             if existing_request['status'] == 'approved':
#                 return jsonify({
#                     'message': 'You already have permission to join this stream',
#                     'status': 'approved',
#                     'can_join': True
#                 }), 200
#             elif existing_request['status'] == 'pending':
#                 return jsonify({
#                     'message': 'Your request is already pending approval for this stream',
#                     'status': 'pending',
#                     'can_join': False
#                 }), 200

#         # Get student and teacher names for notification
#         student_info = db.execute_query_one("SELECT COALESCE(first_name || ' ' || last_name, username) as name FROM students WHERE id = %s", (student_id,))
#         teacher_info = db.execute_query_one("SELECT COALESCE(first_name || ' ' || last_name, username) as name FROM kota_teachers WHERE id = %s", (stream['teacher_id'],))

#         student_name = student_info['name'] if student_info else f"Student {student_id}"
#         teacher_name = teacher_info['name'] if teacher_info else f"Teacher {stream['teacher_id']}"

#         # Create new permission request
#         request_id = str(uuid.uuid4())
#         db.execute_query(
#             "INSERT INTO stream_permissions (id, session_id, student_id, faculty_id, request_message, status, requested_at) VALUES (%s, %s, %s, %s, %s, 'pending', NOW())",
#             (request_id, session_id, student_id, faculty_id, message)
#         )

#         # Send real-time notification to faculty
#         socketio.emit('stream_permission_request', {
#             'request_id': request_id,
#             'session_id': session_id,
#             'student_id': student_id,
#             'student_name': student_name,
#             'teacher_name': teacher_name,
#             'stream_title': f"{teacher_name}'s Live Class",
#             'message': message,
#             'requested_at': datetime.now(timezone.utc).isoformat()
#         }, room=faculty_id)

#         print(f"📝 Stream permission request: {student_name} wants to join {teacher_name}'s stream")

#         return jsonify({
#             'success': True,
#             'request_id': request_id,
#             'message': 'Permission request sent successfully',
#             'status': 'pending'
#         }), 200

#     except Exception as e:
#         print(f"❌ Error requesting stream permission: {e}")
#         return jsonify({'message': 'Failed to request stream permission', 'error': str(e)}), 500

# @app.route('/api/stream-permission-response', methods=['POST'])
# def respond_stream_permission():
#     """Faculty approves or rejects stream permission request"""
#     try:
#         data = request.get_json()
#         request_id = data.get('request_id')
#         action = data.get('action')  # 'approve' or 'reject'
#         faculty_id = data.get('faculty_id')
#         response_message = data.get('response_message', '')

#         if not all([request_id, action, faculty_id]):
#             return jsonify({'message': 'request_id, action, and faculty_id are required'}), 400

#         if action not in ['approve', 'reject']:
#             return jsonify({'message': 'action must be either "approve" or "reject"'}), 400

#         # Get request details
#         request_details = db.execute_query_one(
#             "SELECT * FROM stream_permissions WHERE id = %s AND faculty_id = %s",
#             (request_id, faculty_id)
#         )

#         if not request_details:
#             return jsonify({'message': 'Request not found or you are not authorized to respond'}), 404

#         if request_details['status'] != 'pending':
#             return jsonify({'message': f'Request already {request_details["status"]}'}), 400

#         # Update request status
#         db.execute_query(
#             "UPDATE stream_permissions SET status = %s, response_message = %s, responded_at = NOW() WHERE id = %s",
#             (action + 'd', response_message, request_id)  # 'approved' or 'rejected'
#         )

#         # Get student and stream info for notification
#         student_info = db.execute_query_one("SELECT COALESCE(first_name || ' ' || last_name, username) as name FROM students WHERE id = %s", (request_details['student_id'],))
#         student_name = student_info['name'] if student_info else f"Student {request_details['student_id']}"

#         if action == 'approve':
#             # Generate LiveKit token for approved student
#             stream = enhanced_stream_manager.get_stream(request_details['session_id'])
#             if stream:
#                 token = livekit_manager.generate_access_token(
#                     room_name=request_details['session_id'],
#                     participant_identity=request_details['student_id'],
#                     participant_name=student_name,
#                     is_teacher=False
#                 )

#                 # Notify student of approval
#                 socketio.emit('stream_permission_approved', {
#                     'request_id': request_id,
#                     'session_id': request_details['session_id'],
#                     'livekit_token': token,
#                     'livekit_url': LIVEKIT_URL,
#                     'message': response_message or 'Your request has been approved! You can now join the stream.',
#                     'approved_at': datetime.now(timezone.utc).isoformat()
#                 }, room=request_details['student_id'])

#                 print(f"✅ Stream permission approved: {student_name} can join stream {request_details['session_id']}")
#             else:
#                 return jsonify({'message': 'Stream no longer active'}), 404
#         else:
#             # Notify student of rejection
#             socketio.emit('stream_permission_rejected', {
#                 'request_id': request_id,
#                 'session_id': request_details['session_id'],
#                 'message': response_message or 'Your request has been rejected.',
#                 'rejected_at': datetime.now(timezone.utc).isoformat()
#             }, room=request_details['student_id'])

#             print(f"❌ Stream permission rejected: {student_name} cannot join stream {request_details['session_id']}")

#         return jsonify({
#             'success': True,
#             'message': f'Request {action}d successfully',
#             'action': action + 'd',
#             'student_name': student_name
#         }), 200

#     except Exception as e:
#         print(f"❌ Error responding to stream permission: {e}")
#         return jsonify({'message': 'Failed to respond to stream permission', 'error': str(e)}), 500

# @app.route('/api/stream-permission-status/<student_id>/<session_id>', methods=['GET'])
# def check_permission_status(student_id, session_id):
#     """Check student's permission status for a specific stream"""
#     try:
#         permission = db.execute_query_one(
#             "SELECT status, request_message, response_message, requested_at, responded_at FROM stream_permissions WHERE student_id = %s AND session_id = %s ORDER BY requested_at DESC LIMIT 1",
#             (student_id, session_id)
#         )

#         if not permission:
#             return jsonify({
#                 'status': 'none',
#                 'can_join': False,
#                 'message': 'No permission request found'
#             }), 200

#         return jsonify({
#             'status': permission['status'],
#             'can_join': permission['status'] == 'approved',
#             'request_message': permission['request_message'],
#             'response_message': permission['response_message'],
#             'requested_at': permission['requested_at'].isoformat() if permission['requested_at'] else None,
#             'responded_at': permission['responded_at'].isoformat() if permission['responded_at'] else None
#         }), 200

#     except Exception as e:
#         print(f"❌ Error checking permission status: {e}")
#         return jsonify({'message': 'Failed to check permission status', 'error': str(e)}), 500

# @app.route('/api/stream-permission-requests/<faculty_id>', methods=['GET'])
# def get_faculty_permission_requests(faculty_id):
#     """Get all permission requests for a faculty member"""
#     try:
#         # Get all requests for this faculty member (not just pending)
#         requests = db.execute_query_all(
#             """SELECT sp.*,
#                       COALESCE(s.first_name || ' ' || s.last_name, s.username) as student_name,
#                       COALESCE(f.first_name || ' ' || f.last_name, f.username) as faculty_name
#                FROM stream_permissions sp
#                LEFT JOIN students s ON sp.student_id::uuid = s.id
#                LEFT JOIN faculty f ON sp.faculty_id::uuid = f.id
#                WHERE sp.faculty_id::uuid = %s
#                ORDER BY sp.requested_at DESC""",
#             (faculty_id,)
#         )

#         if not requests:
#             return jsonify({
#                 'success': True,
#                 'requests': [],
#                 'total_requests': 0,
#                 'pending_requests': 0
#             }), 200

#         # Get stream info for each request
#         enriched_requests = []
#         pending_count = 0

#         for req in requests:
#             # Count pending requests
#             if req['status'] == 'pending':
#                 pending_count += 1

#             # Get stream info if available
#             stream = enhanced_stream_manager.get_stream(req['session_id'])
#             stream_info = None
#             if stream:
#                 stream_info = {
#                     'viewer_count': stream['viewer_count'],
#                     'quality': stream['quality'],
#                     'uptime': (datetime.now(timezone.utc) - stream['created_at']).total_seconds(),
#                     'status': stream['status']
#                 }

#             enriched_requests.append({
#                 'request_id': req['id'],
#                 'session_id': req['session_id'],
#                 'student_id': req['student_id'],
#                 'student_name': req['student_name'] or f"Student {req['student_id']}",
#                 'faculty_name': req['faculty_name'] or f"Faculty {req['faculty_id']}",
#                 'status': req['status'],
#                 'request_message': req['request_message'],
#                 'response_message': req['response_message'],
#                 'requested_at': req['requested_at'].isoformat() if req['requested_at'] else None,
#                 'responded_at': req['responded_at'].isoformat() if req['responded_at'] else None,
#                 'stream_info': stream_info
#             })

#         return jsonify({
#             'success': True,
#             'requests': enriched_requests,
#             'total_requests': len(enriched_requests),
#             'pending_requests': pending_count
#         }), 200

#     except Exception as e:
#         print(f"❌ Error getting faculty permission requests: {e}")
#         return jsonify({'message': 'Failed to get permission requests', 'error': str(e)}), 500

# @app.route('/api/user-role/<user_id>', methods=['GET'])
# def get_user_role_endpoint(user_id):
#     """Debug endpoint to check user role"""
#     try:
#         role = get_user_role_by_id(user_id)
#         privileged_roles = ['faculty', 'kota_teacher', 'center_counselor', 'teacher']

#         return jsonify({
#             'user_id': user_id,
#             'role': role,
#             'needs_permission': role == 'student',
#             'can_join_directly': role in privileged_roles,
#             'is_privileged': role in privileged_roles
#         }), 200

#     except Exception as e:
#         print(f"❌ Error getting user role: {e}")
#         return jsonify({'message': 'Failed to get user role', 'error': str(e)}), 500

# @app.route('/api/enhanced-stream/list', methods=['GET'])
# def list_enhanced_streams():
#     """List all enhanced streams (active and inactive)"""
#     try:
#         all_streams = []
#         for session_id, stream in enhanced_stream_manager.streams.items():
#             stream_info = {
#                 'session_id': session_id,
#                 'teacher_id': stream['teacher_id'],
#                 'status': stream['status'],
#                 'viewer_count': stream['viewer_count'],
#                 'quality': stream['quality'],
#                 'created_at': stream['created_at'].isoformat(),
#                 'uptime': (datetime.now(timezone.utc) - stream['created_at']).total_seconds(),
#                 'features': {
#                     'chat_enabled': stream['chat_enabled'],
#                     'recording_enabled': stream['recording_enabled'],
#                     'screen_sharing': stream['screen_sharing']
#                 }
#             }
#             all_streams.append(stream_info)
#         for session_id, recording in enhanced_stream_manager.recordings.items():
#             recording_info = {
#                 'session_id': session_id,
#                 'teacher_id': recording['teacher_id'],
#                 'status': 'recorded',
#                 'duration': recording['duration'],
#                 'quality': recording['quality'],
#                 'viewer_count': recording['viewer_count'],
#                 'chat_messages': recording['chat_messages'],
#                 'recorded_at': recording['recorded_at'].isoformat()
#             }
#             all_streams.append(recording_info)
#         return jsonify({
#             'streams': all_streams,
#             'total_count': len(all_streams),
#             'active_count': len([s for s in all_streams if s['status'] == 'active']),
#             'recorded_count': len([s for s in all_streams if s['status'] == 'recorded']),
#             'service': 'Enhanced Real-time Streaming',
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }), 200
#     except Exception as e:
#         print(f"❌ Error listing streams: {e}")
#         return jsonify({'message': 'Failed to list streams', 'error': str(e)}), 500

# # Socket.IO Events
# @socketio.on('connect')
# def handle_connect():
#     print(f"✅ Enhanced client connected: {request.sid}")
#     emit('connected', {'message': 'Connected to enhanced streaming service'})

# @socketio.on('disconnect')
# def handle_disconnect():
#     print(f"❌ Enhanced client disconnected: {request.sid}")
#     for session_id in list(enhanced_stream_manager.streams.keys()):
#         enhanced_stream_manager.remove_viewer(session_id, request.sid)

# @socketio.on('start_stream')
# def handle_start_stream(data):
#     try:
#         session_id = data.get('session_id')
#         teacher_id = data.get('teacher_id')
#         teacher_name = data.get('teacher_name', teacher_id)
#         quality = data.get('quality', 'medium')

#         if not session_id or not teacher_id:
#             emit('error', {'message': 'Session ID and Teacher ID required'})
#             return

#         stream = enhanced_stream_manager.create_enhanced_stream(
#             teacher_id, session_id, request.sid, quality
#         )
#         join_room(session_id)
#         teacher_token = livekit_manager.generate_access_token(
#             room_name=session_id,
#             participant_identity=teacher_id,
#             participant_name=teacher_name,
#             is_teacher=True
#         )
#         emit('stream_started', {
#             'session_id': session_id,
#             'message': 'Stream started successfully with LiveKit',
#             'livekit_url': LIVEKIT_URL,
#             'livekit_token': teacher_token,
#             'features': {
#                 'chat': True,
#                 'recording': True,
#                 'quality_controls': True,
#                 'screen_sharing': True,
#                 'livekit_enabled': True
#             }
#         })
#         print(f"🎬 Teacher {teacher_id} started LiveKit stream {session_id}")
#     except Exception as e:
#         print(f"❌ Error starting stream: {e}")
#         emit('error', {'message': f'Failed to start stream: {str(e)}'})

# @socketio.on('stop_stream')
# def handle_stop_stream(data):
#     try:
#         session_id = data.get('session_id')
#         teacher_id = data.get('teacher_id')
#         print(f"🛑 Socket stop_stream request: session={session_id}, teacher={teacher_id}")

#         if not session_id and teacher_id:
#             for sid, stream in enhanced_stream_manager.streams.items():
#                 if stream.get('teacher_id') == teacher_id and stream.get('status') == 'active':
#                     session_id = sid
#                     print(f"🔍 Found active stream for teacher {teacher_id}: {session_id}")
#                     break

#         if not session_id:
#             emit('error', {'message': 'Session ID required or no active stream found'})
#             return

#         success = enhanced_stream_manager.stop_stream(session_id)
#         if success:
#             socketio.emit('stream_ended', {
#                 'session_id': session_id,
#                 'message': 'Stream has ended'
#             }, room=session_id)
#             leave_room(session_id)
#             emit('stream_stopped', {
#                 'session_id': session_id,
#                 'message': 'Stream stopped successfully'
#             })
#             print(f"✅ Teacher {teacher_id} stopped stream {session_id}")
#         else:
#             emit('error', {'message': 'Failed to stop stream - stream not found'})
#     except Exception as e:
#         print(f"❌ Error stopping stream: {e}")
#         emit('error', {'message': f'Failed to stop stream: {str(e)}'})

# @socketio.on('end_stream')
# def handle_end_stream(data):
#     try:
#         session_id = data.get('session_id')
#         teacher_id = data.get('teacher_id')
#         print(f"🔄 Received end_stream event for session {session_id}, teacher {teacher_id}")
#         print(f"🔄 Forwarding to stop_stream handler for backward compatibility")
#         handle_stop_stream(data)
#     except Exception as e:
#         print(f"❌ Error handling end_stream: {e}")
#         emit('error', {'message': f'Failed to end stream: {str(e)}'})

# @socketio.on('join_stream')
# def handle_join_stream(data):
#     try:
#         session_id = data.get('session_id')
#         faculty_id = data.get('faculty_id') or data.get('viewer_id') or data.get('user_id')
#         user_name = data.get('user_name') or data.get('viewer_name') or faculty_id
#         user_role = data.get('user_role', 'student')

#         if not session_id:
#             emit('error', {'message': 'Session ID required'})
#             return

#         stream = enhanced_stream_manager.get_stream(session_id)
#         if not stream:
#             emit('error', {'message': 'Stream not found'})
#             return

#         join_room(session_id)
#         viewer_data = {
#             'viewer_id': faculty_id,
#             'viewer_name': user_name,
#             'user_role': user_role,
#             'socket_id': request.sid,
#             'joined_at': datetime.now(timezone.utc)
#         }
#         if 'viewer_details' not in stream:
#             stream['viewer_details'] = {}
#         stream['viewer_details'][faculty_id] = viewer_data

#         success = enhanced_stream_manager.add_viewer(session_id, faculty_id, request.sid)
#         if success:
#             is_teacher = (user_role in ['faculty', 'kota_teacher'] or faculty_id == stream.get('teacher_id'))
#             viewer_token = livekit_manager.generate_access_token(
#                 room_name=session_id,
#                 participant_identity=faculty_id,
#                 participant_name=user_name,
#                 is_teacher=is_teacher
#             )
#             emit('stream_joined', {
#                 'session_id': session_id,
#                 'viewer_count': stream['viewer_count'],
#                 'message': 'Successfully joined stream',
#                 'livekit_url': LIVEKIT_URL,
#                 'livekit_token': viewer_token,
#                 'is_teacher': is_teacher,
#                 'stream_info': {
#                     'teacher_id': stream['teacher_id'],
#                     'quality': stream['quality'],
#                     'features': {
#                         'chat_enabled': stream['chat_enabled'],
#                         'recording_enabled': stream['recording_enabled'],
#                         'screen_sharing': stream['screen_sharing']
#                     }
#                 }
#             })
#             emit('viewer_joined', {
#                 'viewer_id': faculty_id,
#                 'viewer_name': user_name,
#                 'viewer_count': stream['viewer_count'],
#                 'is_teacher': is_teacher,
#                 'user_role': user_role
#             }, room=session_id, include_self=False)
#             print(f"👥 Viewer {faculty_id} joined LiveKit stream {session_id}")
#         else:
#             emit('error', {'message': 'Failed to join stream'})
#     except Exception as e:
#         print(f"❌ Error joining stream: {e}")
#         emit('error', {'message': f'Failed to join stream: {str(e)}'})

# @socketio.on('leave_stream')
# def handle_leave_stream(data):
#     try:
#         session_id = data.get('session_id')
#         faculty_id = data.get('faculty_id') or data.get('viewer_id')
#         if session_id:
#             leave_room(session_id)
#             enhanced_stream_manager.remove_viewer(session_id, request.sid)
#             stream = enhanced_stream_manager.get_stream(session_id)
#             if stream:
#                 if 'viewer_details' in stream and faculty_id in stream['viewer_details']:
#                     del stream['viewer_details'][faculty_id]
#                 emit('viewer_left', {
#                     'viewer_id': faculty_id,
#                     'viewer_count': stream['viewer_count']
#                 }, room=session_id)
#                 print(f"👋 Viewer {faculty_id} left stream {session_id}")
#     except Exception as e:
#         print(f"❌ Error leaving stream: {e}")

# @socketio.on('join_enhanced_stream')
# def handle_join_enhanced_stream(data):
#     try:
#         session_id = data.get('session_id')
#         viewer_id = data.get('viewer_id')
#         viewer_name = data.get('viewer_name', viewer_id)
#         user_role = data.get('user_role', 'student')

#         if not session_id:
#             emit('error', {'message': 'Session ID required'})
#             return

#         join_room(session_id)
#         success = enhanced_stream_manager.add_viewer(session_id, viewer_id, request.sid)
#         if success:
#             stream = enhanced_stream_manager.get_stream(session_id)
#             is_teacher = (user_role in ['faculty', 'kota_teacher'] or viewer_id == stream.get('teacher_id'))
#             viewer_token = livekit_manager.generate_access_token(
#                 room_name=session_id,
#                 participant_identity=viewer_id,
#                 participant_name=viewer_name,
#                 is_teacher=is_teacher
#             )
#             emit('joined_enhanced_stream', {
#                 'session_id': session_id,
#                 'viewer_count': stream['viewer_count'],
#                 'quality_settings': quality_settings.get(session_id, {}),
#                 'chat_history': chat_messages.get(session_id, [])[-50:],
#                 'livekit_url': LIVEKIT_URL,
#                 'livekit_token': viewer_token,
#                 'is_teacher': is_teacher
#             })
#             emit('viewer_joined', {
#                 'viewer_id': viewer_id,
#                 'viewer_name': viewer_name,
#                 'viewer_count': stream['viewer_count'],
#                 'is_teacher': is_teacher
#             }, room=session_id, include_self=False)
#         else:
#             emit('error', {'message': 'Failed to join stream'})
#     except Exception as e:
#         print(f"❌ Error joining enhanced stream: {e}")
#         emit('error', {'message': 'Failed to join stream'})

# @socketio.on('request_livekit_token')
# def handle_request_livekit_token(data):
#     try:
#         session_id = data.get('session_id')
#         user_id = data.get('user_id')
#         user_name = data.get('user_name', user_id)
#         user_role = data.get('user_role', 'student')

#         if not session_id or not user_id:
#             emit('error', {'message': 'Session ID and User ID required'})
#             return

#         stream = enhanced_stream_manager.get_stream(session_id)
#         if not stream:
#             emit('error', {'message': 'Stream not found'})
#             return

#         is_teacher = (user_role in ['faculty', 'kota_teacher'] or user_id == stream.get('teacher_id'))
#         token = livekit_manager.generate_access_token(
#             room_name=session_id,
#             participant_identity=user_id,
#             participant_name=user_name,
#             is_teacher=is_teacher
#         )
#         if token:
#             emit('livekit_token_generated', {
#                 'session_id': session_id,
#                 'livekit_url': LIVEKIT_URL,
#                 'livekit_token': token,
#                 'participant_id': user_id,
#                 'participant_name': user_name,
#                 'is_teacher': is_teacher
#             })
#         else:
#             emit('error', {'message': 'Failed to generate LiveKit token'})
#     except Exception as e:
#         print(f"❌ Error generating LiveKit token: {e}")
#         emit('error', {'message': 'Failed to generate token'})

# @socketio.on('leave_enhanced_stream')
# def handle_leave_enhanced_stream(data):
#     try:
#         session_id = data.get('session_id')
#         if session_id:
#             leave_room(session_id)
#             enhanced_stream_manager.remove_viewer(session_id, request.sid)
#             stream = enhanced_stream_manager.get_stream(session_id)
#             if stream:
#                 emit('viewer_left', {
#                     'viewer_count': stream['viewer_count']
#                 }, room=session_id)
#     except Exception as e:
#         print(f"❌ Error leaving enhanced stream: {e}")

# @socketio.on('enhanced_video_frame')
# def handle_enhanced_video_frame(data):
#     try:
#         session_id = data.get('session_id')
#         frame_data = data.get('frame_data')
#         quality = data.get('quality', 'medium')
#         if not session_id or not frame_data:
#             return
#         if session_id in quality_settings:
#             quality_settings[session_id]['video_quality'] = quality
#         socketio.emit('enhanced_video_frame', {
#             'session_id': session_id,
#             'frame_data': frame_data,
#             'quality': quality,
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }, room=session_id, include_self=False)
#     except Exception as e:
#         print(f"❌ Error handling enhanced video frame: {e}")

# @socketio.on('chat_message')
# def handle_chat_message(data):
#     try:
#         session_id = data.get('session_id')
#         message = data.get('message')
#         sender_id = data.get('sender_id')
#         sender_name = data.get('sender_name', 'Anonymous')
#         if not session_id or not message:
#             return
#         sender_role = get_user_role_by_id(sender_id) or 'unknown'
#         if session_id not in chat_messages:
#             chat_messages[session_id] = []
#         chat_data = {
#             'id': str(uuid.uuid4()),
#             'session_id': session_id,
#             'sender_id': sender_id,
#             'sender_name': sender_name,
#             'sender_role': sender_role,
#             'message': message,
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }
#         chat_messages[session_id].append(chat_data)
#         socketio.emit('chat_message', chat_data, room=session_id)
#         print(f"💬 Chat message from {sender_name} ({sender_role}) in session {session_id}: {message}")
#     except Exception as e:
#         print(f"❌ Error handling chat message: {e}")

# @socketio.on('get_chat_history')
# def handle_get_chat_history(data):
#     try:
#         session_id = data.get('session_id')
#         if session_id and session_id in chat_messages:
#             history = chat_messages[session_id][-50:]
#             emit('chat_history', {
#                 'session_id': session_id,
#                 'messages': history
#             })
#         else:
#             emit('chat_history', {
#                 'session_id': session_id,
#                 'messages': []
#             })
#     except Exception as e:
#         print(f"❌ Error getting chat history: {e}")

# @socketio.on('video_frame')
# def handle_video_frame_legacy(data):
#     try:
#         session_id = data.get('session_id')
#         frame_data = data.get('frame_data')
#         frame_type = data.get('frame_type', 'camera')
#         if not session_id or not frame_data:
#             return
#         socketio.emit('video_frame', {
#             'session_id': session_id,
#             'frame_data': frame_data,
#             'frame_type': frame_type,
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }, room=session_id, include_self=False)
#     except Exception as e:
#         print(f"❌ Error handling video frame: {e}")

# @socketio.on('screen_frame')
# def handle_screen_frame_legacy(data):
#     try:
#         session_id = data.get('session_id')
#         frame_data = data.get('frame_data')
#         if not session_id or not frame_data:
#             return
#         socketio.emit('screen_frame', {
#             'session_id': session_id,
#             'frame_data': frame_data,
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }, room=session_id, include_self=False)
#     except Exception as e:
#         print(f"❌ Error handling screen frame: {e}")

# # NEW SOCKET.IO EVENTS FOR STREAM PERMISSIONS

# @socketio.on('join_faculty_room')
# def handle_join_faculty_room(data):
#     """Faculty joins their notification room"""
#     try:
#         faculty_id = data.get('faculty_id')
#         if faculty_id:
#             join_room(faculty_id)
#             emit('joined_faculty_room', {'faculty_id': faculty_id})
#             print(f"👨‍🏫 Faculty {faculty_id} joined notification room")
#     except Exception as e:
#         print(f"❌ Error joining faculty room: {e}")

# @socketio.on('join_student_room')
# def handle_join_student_room(data):
#     """Student joins their notification room"""
#     try:
#         student_id = data.get('student_id')
#         if student_id:
#             join_room(student_id)
#             emit('joined_student_room', {'student_id': student_id})
#             print(f"👨‍🎓 Student {student_id} joined notification room")
#     except Exception as e:
#         print(f"❌ Error joining student room: {e}")

# @socketio.on('request_stream_permission')
# def handle_request_stream_permission_socket(data):
#     """Handle stream permission request via Socket.IO (alternative to HTTP)"""
#     try:
#         session_id = data.get('session_id')
#         student_id = data.get('student_id')
#         faculty_id = data.get('faculty_id')
#         message = data.get('message', 'Please allow me to join the live stream')

#         if not all([session_id, student_id, faculty_id]):
#             emit('error', {'message': 'session_id, student_id, and faculty_id are required'})
#             return

#         # Check if stream exists
#         stream = enhanced_stream_manager.get_stream(session_id)
#         if not stream:
#             emit('error', {'message': 'Stream not found'})
#             return

#         # Check for existing request
#         existing_request = db.execute_query_one(
#             "SELECT id, status FROM stream_permissions WHERE student_id = %s AND session_id = %s AND status IN ('pending', 'approved') ORDER BY requested_at DESC LIMIT 1",
#             (student_id, session_id)
#         )

#         if existing_request:
#             if existing_request['status'] == 'approved':
#                 emit('permission_already_granted', {'message': 'You already have permission to join this stream'})
#                 return
#             elif existing_request['status'] == 'pending':
#                 emit('permission_already_pending', {'message': 'Your request is already pending approval'})
#                 return

#         # Get names for notification
#         student_info = db.execute_query_one("SELECT COALESCE(first_name || ' ' || last_name, username) as name FROM students WHERE id = %s", (student_id,))
#         teacher_info = db.execute_query_one("SELECT COALESCE(first_name || ' ' || last_name, username) as name FROM kota_teachers WHERE id = %s", (stream['teacher_id'],))

#         student_name = student_info['name'] if student_info else f"Student {student_id}"
#         teacher_name = teacher_info['name'] if teacher_info else f"Teacher {stream['teacher_id']}"

#         # Create new permission request
#         request_id = str(uuid.uuid4())
#         db.execute_query(
#             "INSERT INTO stream_permissions (id, session_id, student_id, faculty_id, request_message, status, requested_at) VALUES (%s, %s, %s, %s, %s, 'pending', NOW())",
#             (request_id, session_id, student_id, faculty_id, message)
#         )

#         # Send notification to faculty
#         socketio.emit('stream_permission_request', {
#             'request_id': request_id,
#             'session_id': session_id,
#             'student_id': student_id,
#             'student_name': student_name,
#             'teacher_name': teacher_name,
#             'stream_title': f"{teacher_name}'s Live Class",
#             'message': message,
#             'requested_at': datetime.now(timezone.utc).isoformat()
#         }, room=faculty_id)

#         # Confirm to student
#         emit('permission_request_sent', {
#             'request_id': request_id,
#             'message': 'Permission request sent successfully',
#             'status': 'pending'
#         })

#         print(f"📝 Socket.IO Stream permission request: {student_name} wants to join {teacher_name}'s stream")

#     except Exception as e:
#         print(f"❌ Error handling stream permission request via Socket.IO: {e}")
#         emit('error', {'message': 'Failed to request stream permission'})

# if __name__ == '__main__':
#     print("🚀 Starting Enhanced Real-time Streaming Service with LiveKit...")
#     print("🎯 Features: LiveKit + WebRTC + Socket.IO + Chat + Recording + Quality Controls")
#     print("🌐 CORS enabled for all origins with full preflight support")
#     print("🔧 Socket.IO CORS configured with credentials support")
#     print(f"🎥 LiveKit URL: {LIVEKIT_URL}")
#     print(f"🔑 LiveKit API Key: {LIVEKIT_API_KEY}")
#     print("🚀 Server starting on port 8012...")
#     print("=" * 70)

#     if not all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]):
#         print("❌ LiveKit configuration missing! Please check your .env file.")
#         print("Required: LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET")
#         exit(1)

#     print("✅ LiveKit configuration validated")
#     print("🎬 Ready to create streaming rooms with LiveKit integration")
#     DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() in ['true', '1', 't']

#     # For production deployment, use allow_unsafe_werkzeug=True
#     # Note: For better production performance, consider using Gunicorn
#     socketio.run(app, host='0.0.0.0', port=8012, debug=DEBUG, allow_unsafe_werkzeug=True)

from flask import Flask, request, jsonify
from flask_socketio import SocketIO, emit, join_room, leave_room
from flask_cors import CORS
import sys
import os
import json
import uuid
from datetime import datetime, timezone, timedelta
import base64
import asyncio
import threading

# LiveKit imports
from livekit import api, rtc
from livekit.api import AccessToken, VideoGrants, LiveKitAPI
from livekit.rtc import Room, TrackSource

# Add parent directory to path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv
from shared.database import Database  # Import Database

# Import ProxyFix for handling reverse proxy headers
from werkzeug.middleware.proxy_fix import ProxyFix

load_dotenv()

app = Flask(__name__)
app.config['SECRET_KEY'] = 'enhanced-streaming-secret-key'

# Configure CORS to allow all origins for development
CORS(app,
     origins="*",
     allow_headers=["Content-Type", "Authorization", "Access-Control-Allow-Credentials"],
     supports_credentials=True,
     methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"])

# Apply ProxyFix middleware to handle Nginx proxy headers
app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_port=1, x_prefix=1)

# Initialize Socket.IO with CORS support for all origins
# async_mode will be auto-detected (eventlet in production, threading in development)
socketio = SocketIO(app,
                   cors_allowed_origins="*",
                   cors_credentials=True,
                   logger=True,
                   engineio_logger=True)

# Initialize Database
db = Database()

# Add CORS preflight handler
@app.before_request
def handle_preflight():
    if request.method == "OPTIONS":
        response = jsonify({'status': 'OK'})
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add('Access-Control-Allow-Headers', "*")
        response.headers.add('Access-Control-Allow-Methods', "*")
        return response

# LiveKit Configuration from environment variables
LIVEKIT_URL = os.getenv('LIVEKIT_URL')
LIVEKIT_API_KEY = os.getenv('LIVEKIT_API_KEY')
LIVEKIT_API_SECRET = os.getenv('LIVEKIT_API_SECRET')

# Initialize LiveKit API Client - Lazy Initialization
livekit_api = None

def get_livekit_api():
    """Get or create LiveKit API client"""
    global livekit_api
    if livekit_api is None:
        livekit_api = LiveKitAPI(LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
    return livekit_api

# Global storage for enhanced streaming features
enhanced_streams = {}
stream_recordings = {}
chat_messages = {}
quality_settings = {}
livekit_rooms = {}  # Track LiveKit rooms

class LiveKitManager:
    """LiveKit integration manager for room and token management"""

    def _init_(self):
        self.rooms = {}

    def generate_access_token(self, room_name, participant_identity, participant_name=None, is_teacher=False):
        """Generate LiveKit access token for a participant"""
        try:
            token = AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
            token.with_identity(participant_identity)
            token.with_name(participant_name or participant_identity)

            grants = VideoGrants(
                room_join=True,
                room=room_name,
                can_publish=is_teacher,
                can_subscribe=True,
                can_publish_data=True,
                can_update_own_metadata=True
            )

            if is_teacher:
                grants.room_admin = True

            token.with_grants(grants)
            token.with_ttl(timedelta(hours=24))
            jwt_token = token.to_jwt()
            print(f"✅ Generated LiveKit token for {participant_identity} in room {room_name}")
            return jwt_token
        except Exception as e:
            print(f"❌ Error generating LiveKit token: {e}")
            return None

    async def create_room(self, room_name, max_participants=50):
        """Create a LiveKit room"""
        try:
            room_request = api.CreateRoomRequest(
                name=room_name,
                max_participants=max_participants,
                empty_timeout=10 * 60,
                departure_timeout=60
            )
            api_client = get_livekit_api()
            room = await api_client.room.create_room(room_request)
            self.rooms[room_name] = {
                'room': room,
                'created_at': datetime.now(timezone.utc),
                'participants': [],
                'max_participants': max_participants
            }
            print(f"✅ Created LiveKit room: {room_name}")
            return room
        except Exception as e:
            print(f"❌ Error creating LiveKit room {room_name}: {e}")
            return None

    async def delete_room(self, room_name):
        """Delete a LiveKit room"""
        try:
            api_client = get_livekit_api()
            await api_client.room.delete_room(api.DeleteRoomRequest(room=room_name))
            if room_name in self.rooms:
                del self.rooms[room_name]
            print(f"✅ Deleted LiveKit room: {room_name}")
            return True
        except Exception as e:
            print(f"❌ Error deleting LiveKit room {room_name}: {e}")
            return False

    async def list_participants(self, room_name):
        """List participants in a LiveKit room"""
        try:
            api_client = get_livekit_api()
            response = await api_client.room.list_participants(api.ListParticipantsRequest(room=room_name))
            return response.participants
        except Exception as e:
            print(f"❌ Error listing participants in room {room_name}: {e}")
            return []

    async def remove_participant(self, room_name, participant_identity):
        """Remove a participant from a LiveKit room"""
        try:
            api_client = get_livekit_api()
            await api_client.room.remove_participant(api.RoomParticipantIdentity(
                room=room_name,
                identity=participant_identity
            ))
            print(f"✅ Removed participant {participant_identity} from room {room_name}")
            return True
        except Exception as e:
            print(f"❌ Error removing participant {participant_identity} from room {room_name}: {e}")
            return False

# Initialize LiveKit Manager
livekit_manager = LiveKitManager()

class EnhancedStreamManager:
    def _init_(self):
        self.streams = {}
        self.connections = {}
        self.recordings = {}

    def create_enhanced_stream(self, teacher_id, session_id, socket_id, quality='medium'):
        """Create an enhanced streaming session with LiveKit integration"""
        stream_data = {
            'teacher_id': teacher_id,
            'session_id': session_id,
            'teacher_socket': socket_id,
            'viewers': [],
            'created_at': datetime.now(timezone.utc),
            'status': 'active',
            'quality': quality,
            'recording_enabled': True,
            'chat_enabled': True,
            'screen_sharing': True,
            'viewer_count': 0,
            'livekit_room': session_id,
            'livekit_enabled': True
        }
        self.streams[session_id] = stream_data
        chat_messages[session_id] = []
        quality_settings[session_id] = {
            'video_quality': quality,
            'frame_rate': 30 if quality == 'high' else 20,
            'resolution': '1920x1080' if quality == 'high' else '1280x720'
        }

        def create_livekit_room():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(livekit_manager.create_room(session_id))
            except Exception as e:
                print(f"❌ Error creating LiveKit room: {e}")
            finally:
                loop.close()

        threading.Thread(target=create_livekit_room, daemon=True).start()
        print(f"✅ Enhanced stream with LiveKit created: {session_id} by teacher {teacher_id}")
        return stream_data

    def add_viewer(self, session_id, viewer_id, socket_id):
        """Add a viewer to the enhanced stream"""
        if session_id in self.streams:
            viewer_data = {
                'viewer_id': viewer_id,
                'socket_id': socket_id,
                'joined_at': datetime.now(timezone.utc)
            }
            self.streams[session_id]['viewers'].append(viewer_data)
            self.streams[session_id]['viewer_count'] = len(self.streams[session_id]['viewers'])
            return True
        return False

    def remove_viewer(self, session_id, socket_id):
        """Remove a viewer from the enhanced stream"""
        if session_id in self.streams:
            self.streams[session_id]['viewers'] = [
                v for v in self.streams[session_id]['viewers']
                if v['socket_id'] != socket_id
            ]
            self.streams[session_id]['viewer_count'] = len(self.streams[session_id]['viewers'])
            return True
        return False

    def get_stream(self, session_id):
        """Get enhanced stream information"""
        return self.streams.get(session_id)

    def stop_stream(self, session_id):
        """Stop enhanced stream and save recording"""
        print(f"🛑 Attempting to stop stream: {session_id}")
        if session_id in self.streams:
            stream = self.streams[session_id]
            print(f"📊 Stream found - Teacher: {stream.get('teacher_id')}, Status: {stream.get('status')}, Viewers: {stream.get('viewer_count', 0)}")
            stream['status'] = 'stopped'
            stream['ended_at'] = datetime.now(timezone.utc)

            if stream.get('recording_enabled'):
                duration = (datetime.now(timezone.utc) - stream['created_at']).total_seconds()
                self.recordings[session_id] = {
                    'session_id': session_id,
                    'teacher_id': stream['teacher_id'],
                    'duration': duration,
                    'quality': stream['quality'],
                    'viewer_count': stream['viewer_count'],
                    'chat_messages': len(chat_messages.get(session_id, [])),
                    'recorded_at': datetime.now(timezone.utc)
                }
                print(f"💾 Recording saved - Duration: {duration:.1f}s, Quality: {stream['quality']}")

            if stream.get('livekit_enabled'):
                def delete_livekit_room():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        loop.run_until_complete(livekit_manager.delete_room(session_id))
                    except Exception as e:
                        print(f"❌ Error deleting LiveKit room: {e}")
                    finally:
                        loop.close()
                threading.Thread(target=delete_livekit_room, daemon=True).start()

            if session_id in chat_messages:
                del chat_messages[session_id]
                print(f"🧹 Cleaned up chat messages for session {session_id}")
            if session_id in quality_settings:
                del quality_settings[session_id]
                print(f"🧹 Cleaned up quality settings for session {session_id}")

            del self.streams[session_id]
            print(f"✅ Stream {session_id} stopped and cleaned up successfully")
            return True
        else:
            print(f"❌ Stream {session_id} not found in active streams")
            print(f"📋 Active streams: {list(self.streams.keys())}")
            return False

enhanced_stream_manager = EnhancedStreamManager()

def get_user_role_by_id(user_id):
    """Helper function to get user role by ID from various tables."""
    user = db.execute_query_one("SELECT role FROM users WHERE id = %s", (user_id,))
    if user:
        return user['role']
    center = db.execute_query_one("SELECT 'center_counselor' as role FROM centers WHERE center_code = %s", (user_id,))
    if center:
        return center['role']
    student = db.execute_query_one("SELECT 'student' as role FROM students WHERE id = %s", (user_id,))
    if student:
        return student['role']
    parent = db.execute_query_one("SELECT 'parent' as role FROM parents WHERE id = %s", (user_id,))
    if parent:
        return parent['role']
    faculty = db.execute_query_one("SELECT 'faculty' as role FROM faculty WHERE id = %s", (user_id,))
    if faculty:
        return faculty['role']
    teacher = db.execute_query_one("SELECT 'kota_teacher' as role FROM kota_teachers WHERE id = %s", (user_id,))
    if teacher:
        return teacher['role']
    return None

@app.route('/health', methods=['GET'])
def health_check():
    """Enhanced streaming service health check with LiveKit status"""
    livekit_status = 'configured' if all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]) else 'not configured'
    return jsonify({
        'status': 'healthy',
        'service': 'Enhanced Real-time Streaming Service with LiveKit',
        'port': 8012,  # Updated to match the running port
        'features': ['LiveKit', 'WebRTC', 'Socket.IO', 'Chat', 'Recording', 'Quality Controls'],
        'active_streams': len(enhanced_stream_manager.streams),
        'livekit': {
            'status': livekit_status,
            'url': LIVEKIT_URL,
            'api_key': LIVEKIT_API_KEY[:8] + '...' if LIVEKIT_API_KEY else None,
            'rooms_managed': len(livekit_manager.rooms)
        },
        'timestamp': datetime.now().isoformat()
    }), 200

# HTTP-based Chat API Endpoints
@app.route('/api/chat/send', methods=['POST'])
def send_chat_message():
    """Send a chat message via HTTP"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        message = data.get('message')
        sender_id = data.get('sender_id')
        sender_name = data.get('sender_name', 'Anonymous')

        if not session_id or not message:
            return jsonify({'message': 'session_id and message are required'}), 400

        # Get sender role
        sender_role = get_user_role_by_id(sender_id) or 'unknown'

        # Initialize chat messages for session if not exists
        if session_id not in chat_messages:
            chat_messages[session_id] = []

        # Create chat message
        chat_data = {
            'id': str(uuid.uuid4()),
            'session_id': session_id,
            'sender_id': sender_id,
            'sender_name': sender_name,
            'sender_role': sender_role,
            'message': message,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

        # Store message
        chat_messages[session_id].append(chat_data)

        # Also emit via Socket.IO for real-time updates (if anyone is connected)
        socketio.emit('chat_message', chat_data, room=session_id)

        print(f"💬 HTTP Chat message from {sender_name} ({sender_role}) in session {session_id}: {message}")

        return jsonify({
            'success': True,
            'message': 'Message sent successfully',
            'chat_data': chat_data
        }), 200

    except Exception as e:
        print(f"❌ Error sending chat message via HTTP: {e}")
        return jsonify({'message': 'Failed to send message', 'error': str(e)}), 500

@app.route('/api/chat/history/<session_id>', methods=['GET'])
def get_chat_history(session_id):
    """Get chat history for a session via HTTP"""
    try:
        if session_id and session_id in chat_messages:
            history = chat_messages[session_id][-50:]  # Last 50 messages
            return jsonify({
                'success': True,
                'session_id': session_id,
                'messages': history,
                'total_messages': len(history)
            }), 200
        else:
            return jsonify({
                'success': True,
                'session_id': session_id,
                'messages': [],
                'total_messages': 0
            }), 200

    except Exception as e:
        print(f"❌ Error getting chat history via HTTP: {e}")
        return jsonify({'message': 'Failed to get chat history', 'error': str(e)}), 500

@app.route('/api/enhanced-stream/start', methods=['POST'])
def start_enhanced_stream():
    """Start enhanced streaming session with LiveKit"""
    try:
        data = request.get_json()
        session_id = data.get('session_id', str(uuid.uuid4()))
        quality = data.get('quality', 'medium')
        teacher_id = data.get('teacher_id', 'demo_teacher')
        teacher_name = data.get('teacher_name', teacher_id)

        stream = enhanced_stream_manager.create_enhanced_stream(
            teacher_id, session_id, None, quality
        )

        teacher_token = livekit_manager.generate_access_token(
            room_name=session_id,
            participant_identity=teacher_id,
            participant_name=teacher_name,
            is_teacher=True
        )

        # Dynamically generate WebSocket URL
        ws_scheme = 'wss' if request.scheme == 'https' else 'ws'
        stream_url = f"{ws_scheme}://{request.host}/socket.io/"

        return jsonify({
            'message': 'Enhanced stream with LiveKit started successfully',
            'session_id': session_id,
            'stream_url': stream_url,
            'livekit_url': LIVEKIT_URL,
            'livekit_token': teacher_token,
            'features': {
                'chat': True,
                'recording': True,
                'quality_controls': True,
                'screen_sharing': True,
                'livekit_enabled': True
            },
            'quality_settings': quality_settings[session_id]
        }), 200
    except Exception as e:
        print(f"Enhanced stream start error: {e}")
        return jsonify({'message': 'Failed to start enhanced stream'}), 500

@app.route('/api/livekit/token', methods=['POST'])
def generate_livekit_token():
    """Generate LiveKit access token for participants"""
    try:
        data = request.get_json()
        room_name = data.get('room_name') or data.get('session_id')
        participant_id = data.get('participant_id') or data.get('user_id')
        participant_name = data.get('participant_name', participant_id)
        is_teacher = data.get('is_teacher', False)

        if not room_name or not participant_id:
            return jsonify({'message': 'room_name and participant_id are required'}), 400

        stream = enhanced_stream_manager.get_stream(room_name)
        if not stream:
            return jsonify({'message': 'Stream not found'}), 404

        token = livekit_manager.generate_access_token(
            room_name=room_name,
            participant_identity=participant_id,
            participant_name=participant_name,
            is_teacher=is_teacher
        )

        if token:
            return jsonify({
                'token': token,
                'livekit_url': LIVEKIT_URL,
                'room_name': room_name,
                'participant_id': participant_id,
                'is_teacher': is_teacher
            }), 200
        else:
            return jsonify({'message': 'Failed to generate token'}), 500
    except Exception as e:
        print(f"Token generation error: {e}")
        return jsonify({'message': 'Failed to generate token'}), 500

@app.route('/api/livekit/join', methods=['POST'])
def join_livekit_room():
    """Join LiveKit room and get connection details"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        user_id = data.get('user_id')
        user_name = data.get('user_name', user_id)
        user_role = data.get('user_role', 'student')

        if not session_id or not user_id:
            return jsonify({'message': 'session_id and user_id are required'}), 400

        stream = enhanced_stream_manager.get_stream(session_id)
        if not stream:
            return jsonify({'message': 'Stream not found'}), 404

        is_teacher = (user_role in ['faculty', 'kota_teacher'] or user_id == stream.get('teacher_id'))
        token = livekit_manager.generate_access_token(
            room_name=session_id,
            participant_identity=user_id,
            participant_name=user_name,
            is_teacher=is_teacher
        )

        if token:
            return jsonify({
                'success': True,
                'livekit_url': LIVEKIT_URL,
                'token': token,
                'room_name': session_id,
                'participant_id': user_id,
                'participant_name': user_name,
                'is_teacher': is_teacher,
                'stream_info': {
                    'session_id': session_id,
                    'teacher_id': stream['teacher_id'],
                    'viewer_count': stream['viewer_count'],
                    'quality': stream['quality'],
                    'features': {
                        'chat_enabled': stream['chat_enabled'],
                        'recording_enabled': stream['recording_enabled'],
                        'screen_sharing': stream['screen_sharing']
                    }
                }
            }), 200
        else:
            return jsonify({'message': 'Failed to generate access token'}), 500
    except Exception as e:
        print(f"LiveKit join error: {e}")
        return jsonify({'message': 'Failed to join LiveKit room'}), 500

@app.route('/api/enhanced-stream/stop', methods=['POST'])
def stop_enhanced_stream():
    """Stop enhanced streaming session"""
    try:
        data = request.get_json() or {}
        session_id = data.get('session_id')
        teacher_id = data.get('teacher_id')

        print(f"🛑 Received stop request for session: {session_id}, teacher: {teacher_id}")
        if not session_id and teacher_id:
            for sid, stream in enhanced_stream_manager.streams.items():
                if stream.get('teacher_id') == teacher_id and stream.get('status') == 'active':
                    session_id = sid
                    print(f"🔍 Found active stream for teacher {teacher_id}: {session_id}")
                    break

        if not session_id:
            active_sessions = list(enhanced_stream_manager.streams.keys())
            if active_sessions:
                print(f"⚠ No session_id provided, stopping all active streams: {active_sessions}")
                stopped_count = 0
                for sid in active_sessions:
                    if enhanced_stream_manager.stop_stream(sid):
                        socketio.emit('stream_ended', {
                            'session_id': sid,
                            'message': 'Stream has ended'
                        }, room=sid)
                        stopped_count += 1
                print(f"✅ Stopped {stopped_count} streams and notified all viewers")
                return jsonify({
                    'message': f'Stopped {stopped_count} active streams',
                    'stopped_sessions': active_sessions,
                    'recording_saved': True
                }), 200
            else:
                return jsonify({'message': 'No active streams found'}), 404

        success = enhanced_stream_manager.stop_stream(session_id)
        if success:
            socketio.emit('stream_ended', {
                'session_id': session_id,
                'message': 'Stream has ended'
            }, room=session_id)
            print(f"✅ Successfully stopped stream {session_id} and notified viewers")
            return jsonify({
                'message': 'Enhanced stream stopped successfully',
                'session_id': session_id,
                'recording_saved': True
            }), 200
        else:
            print(f"❌ Stream {session_id} not found")
            return jsonify({'message': 'Stream not found'}), 404
    except Exception as e:
        print(f"❌ Enhanced stream stop error: {e}")
        return jsonify({'message': 'Failed to stop enhanced stream', 'error': str(e)}), 500

@app.route('/api/enhanced-stream/status/<session_id>', methods=['GET'])
def get_enhanced_stream_status(session_id):
    """Get enhanced stream status"""
    stream = enhanced_stream_manager.get_stream(session_id)
    if stream:
        return jsonify({
            'session_id': session_id,
            'status': stream['status'],
            'viewer_count': stream['viewer_count'],
            'quality': stream['quality'],
            'features': {
                'chat_enabled': stream['chat_enabled'],
                'recording_enabled': stream['recording_enabled'],
                'screen_sharing': stream['screen_sharing']
            },
            'uptime': (datetime.now(timezone.utc) - stream['created_at']).total_seconds()
        }), 200
    else:
        return jsonify({'message': 'Stream not found'}), 404

@app.route('/active-streams', methods=['GET'])
def get_active_streams():
    """Get all active enhanced streams"""
    try:
        active_streams = []
        for session_id, stream in enhanced_stream_manager.streams.items():
            if stream['status'] == 'active':
                stream_info = {
                    'session_id': session_id,
                    'teacher_id': stream['teacher_id'],
                    'viewer_count': stream['viewer_count'],
                    'quality': stream['quality'],
                    'created_at': stream['created_at'].isoformat(),
                    'uptime': (datetime.now(timezone.utc) - stream['created_at']).total_seconds(),
                    'features': {
                        'chat_enabled': stream['chat_enabled'],
                        'recording_enabled': stream['recording_enabled'],
                        'screen_sharing': stream['screen_sharing']
                    }
                }
                active_streams.append(stream_info)
        return jsonify({
            'success': True,
            'streams': active_streams,
            'active_streams': active_streams,
            'total_count': len(active_streams),
            'service': 'Enhanced Real-time Streaming',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }), 200
    except Exception as e:
        print(f"❌ Error getting active streams: {e}")
        return jsonify({'message': 'Failed to get active streams', 'error': str(e)}), 500

@app.route('/api/enhanced-stream/list', methods=['GET'])
def list_enhanced_streams():
    """List all enhanced streams (active and inactive)"""
    try:
        all_streams = []
        for session_id, stream in enhanced_stream_manager.streams.items():
            stream_info = {
                'session_id': session_id,
                'teacher_id': stream['teacher_id'],
                'status': stream['status'],
                'viewer_count': stream['viewer_count'],
                'quality': stream['quality'],
                'created_at': stream['created_at'].isoformat(),
                'uptime': (datetime.now(timezone.utc) - stream['created_at']).total_seconds(),
                'features': {
                    'chat_enabled': stream['chat_enabled'],
                    'recording_enabled': stream['recording_enabled'],
                    'screen_sharing': stream['screen_sharing']
                }
            }
            all_streams.append(stream_info)
        for session_id, recording in enhanced_stream_manager.recordings.items():
            recording_info = {
                'session_id': session_id,
                'teacher_id': recording['teacher_id'],
                'status': 'recorded',
                'duration': recording['duration'],
                'quality': recording['quality'],
                'viewer_count': recording['viewer_count'],
                'chat_messages': recording['chat_messages'],
                'recorded_at': recording['recorded_at'].isoformat()
            }
            all_streams.append(recording_info)
        return jsonify({
            'streams': all_streams,
            'total_count': len(all_streams),
            'active_count': len([s for s in all_streams if s['status'] == 'active']),
            'recorded_count': len([s for s in all_streams if s['status'] == 'recorded']),
            'service': 'Enhanced Real-time Streaming',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }), 200
    except Exception as e:
        print(f"❌ Error listing streams: {e}")
        return jsonify({'message': 'Failed to list streams', 'error': str(e)}), 500

# Socket.IO Events
@socketio.on('connect')
def handle_connect():
    print(f"✅ Enhanced client connected: {request.sid}")
    emit('connected', {'message': 'Connected to enhanced streaming service'})

@socketio.on('disconnect')
def handle_disconnect():
    print(f"❌ Enhanced client disconnected: {request.sid}")
    for session_id in list(enhanced_stream_manager.streams.keys()):
        enhanced_stream_manager.remove_viewer(session_id, request.sid)

@socketio.on('start_stream')
def handle_start_stream(data):
    try:
        session_id = data.get('session_id')
        teacher_id = data.get('teacher_id')
        teacher_name = data.get('teacher_name', teacher_id)
        quality = data.get('quality', 'medium')

        if not session_id or not teacher_id:
            emit('error', {'message': 'Session ID and Teacher ID required'})
            return

        stream = enhanced_stream_manager.create_enhanced_stream(
            teacher_id, session_id, request.sid, quality
        )
        join_room(session_id)
        teacher_token = livekit_manager.generate_access_token(
            room_name=session_id,
            participant_identity=teacher_id,
            participant_name=teacher_name,
            is_teacher=True
        )
        emit('stream_started', {
            'session_id': session_id,
            'message': 'Stream started successfully with LiveKit',
            'livekit_url': LIVEKIT_URL,
            'livekit_token': teacher_token,
            'features': {
                'chat': True,
                'recording': True,
                'quality_controls': True,
                'screen_sharing': True,
                'livekit_enabled': True
            }
        })
        print(f"🎬 Teacher {teacher_id} started LiveKit stream {session_id}")
    except Exception as e:
        print(f"❌ Error starting stream: {e}")
        emit('error', {'message': f'Failed to start stream: {str(e)}'})

@socketio.on('stop_stream')
def handle_stop_stream(data):
    try:
        session_id = data.get('session_id')
        teacher_id = data.get('teacher_id')
        print(f"🛑 Socket stop_stream request: session={session_id}, teacher={teacher_id}")

        if not session_id and teacher_id:
            for sid, stream in enhanced_stream_manager.streams.items():
                if stream.get('teacher_id') == teacher_id and stream.get('status') == 'active':
                    session_id = sid
                    print(f"🔍 Found active stream for teacher {teacher_id}: {session_id}")
                    break

        if not session_id:
            emit('error', {'message': 'Session ID required or no active stream found'})
            return

        success = enhanced_stream_manager.stop_stream(session_id)
        if success:
            socketio.emit('stream_ended', {
                'session_id': session_id,
                'message': 'Stream has ended'
            }, room=session_id)
            leave_room(session_id)
            emit('stream_stopped', {
                'session_id': session_id,
                'message': 'Stream stopped successfully'
            })
            print(f"✅ Teacher {teacher_id} stopped stream {session_id}")
        else:
            emit('error', {'message': 'Failed to stop stream - stream not found'})
    except Exception as e:
        print(f"❌ Error stopping stream: {e}")
        emit('error', {'message': f'Failed to stop stream: {str(e)}'})

@socketio.on('end_stream')
def handle_end_stream(data):
    try:
        session_id = data.get('session_id')
        teacher_id = data.get('teacher_id')
        print(f"🔄 Received end_stream event for session {session_id}, teacher {teacher_id}")
        print(f"🔄 Forwarding to stop_stream handler for backward compatibility")
        handle_stop_stream(data)
    except Exception as e:
        print(f"❌ Error handling end_stream: {e}")
        emit('error', {'message': f'Failed to end stream: {str(e)}'})

@socketio.on('join_stream')
def handle_join_stream(data):
    try:
        session_id = data.get('session_id')
        faculty_id = data.get('faculty_id') or data.get('viewer_id') or data.get('user_id')
        user_name = data.get('user_name') or data.get('viewer_name') or faculty_id
        user_role = data.get('user_role', 'student')

        if not session_id:
            emit('error', {'message': 'Session ID required'})
            return

        stream = enhanced_stream_manager.get_stream(session_id)
        if not stream:
            emit('error', {'message': 'Stream not found'})
            return

        join_room(session_id)
        viewer_data = {
            'viewer_id': faculty_id,
            'viewer_name': user_name,
            'user_role': user_role,
            'socket_id': request.sid,
            'joined_at': datetime.now(timezone.utc)
        }
        if 'viewer_details' not in stream:
            stream['viewer_details'] = {}
        stream['viewer_details'][faculty_id] = viewer_data

        success = enhanced_stream_manager.add_viewer(session_id, faculty_id, request.sid)
        if success:
            is_teacher = (user_role in ['faculty', 'kota_teacher'] or faculty_id == stream.get('teacher_id'))
            viewer_token = livekit_manager.generate_access_token(
                room_name=session_id,
                participant_identity=faculty_id,
                participant_name=user_name,
                is_teacher=is_teacher
            )
            emit('stream_joined', {
                'session_id': session_id,
                'viewer_count': stream['viewer_count'],
                'message': 'Successfully joined stream',
                'livekit_url': LIVEKIT_URL,
                'livekit_token': viewer_token,
                'is_teacher': is_teacher,
                'stream_info': {
                    'teacher_id': stream['teacher_id'],
                    'quality': stream['quality'],
                    'features': {
                        'chat_enabled': stream['chat_enabled'],
                        'recording_enabled': stream['recording_enabled'],
                        'screen_sharing': stream['screen_sharing']
                    }
                }
            })
            emit('viewer_joined', {
                'viewer_id': faculty_id,
                'viewer_name': user_name,
                'viewer_count': stream['viewer_count'],
                'is_teacher': is_teacher,
                'user_role': user_role
            }, room=session_id, include_self=False)
            print(f"👥 Viewer {faculty_id} joined LiveKit stream {session_id}")
        else:
            emit('error', {'message': 'Failed to join stream'})
    except Exception as e:
        print(f"❌ Error joining stream: {e}")
        emit('error', {'message': f'Failed to join stream: {str(e)}'})

@socketio.on('leave_stream')
def handle_leave_stream(data):
    try:
        session_id = data.get('session_id')
        faculty_id = data.get('faculty_id') or data.get('viewer_id')
        if session_id:
            leave_room(session_id)
            enhanced_stream_manager.remove_viewer(session_id, request.sid)
            stream = enhanced_stream_manager.get_stream(session_id)
            if stream:
                if 'viewer_details' in stream and faculty_id in stream['viewer_details']:
                    del stream['viewer_details'][faculty_id]
                emit('viewer_left', {
                    'viewer_id': faculty_id,
                    'viewer_count': stream['viewer_count']
                }, room=session_id)
                print(f"👋 Viewer {faculty_id} left stream {session_id}")
    except Exception as e:
        print(f"❌ Error leaving stream: {e}")

@socketio.on('join_enhanced_stream')
def handle_join_enhanced_stream(data):
    try:
        session_id = data.get('session_id')
        viewer_id = data.get('viewer_id')
        viewer_name = data.get('viewer_name', viewer_id)
        user_role = data.get('user_role', 'student')

        if not session_id:
            emit('error', {'message': 'Session ID required'})
            return

        join_room(session_id)
        success = enhanced_stream_manager.add_viewer(session_id, viewer_id, request.sid)
        if success:
            stream = enhanced_stream_manager.get_stream(session_id)
            is_teacher = (user_role in ['faculty', 'kota_teacher'] or viewer_id == stream.get('teacher_id'))
            viewer_token = livekit_manager.generate_access_token(
                room_name=session_id,
                participant_identity=viewer_id,
                participant_name=viewer_name,
                is_teacher=is_teacher
            )
            emit('joined_enhanced_stream', {
                'session_id': session_id,
                'viewer_count': stream['viewer_count'],
                'quality_settings': quality_settings.get(session_id, {}),
                'chat_history': chat_messages.get(session_id, [])[-50:],
                'livekit_url': LIVEKIT_URL,
                'livekit_token': viewer_token,
                'is_teacher': is_teacher
            })
            emit('viewer_joined', {
                'viewer_id': viewer_id,
                'viewer_name': viewer_name,
                'viewer_count': stream['viewer_count'],
                'is_teacher': is_teacher
            }, room=session_id, include_self=False)
        else:
            emit('error', {'message': 'Failed to join stream'})
    except Exception as e:
        print(f"❌ Error joining enhanced stream: {e}")
        emit('error', {'message': 'Failed to join stream'})

@socketio.on('request_livekit_token')
def handle_request_livekit_token(data):
    try:
        session_id = data.get('session_id')
        user_id = data.get('user_id')
        user_name = data.get('user_name', user_id)
        user_role = data.get('user_role', 'student')

        if not session_id or not user_id:
            emit('error', {'message': 'Session ID and User ID required'})
            return

        stream = enhanced_stream_manager.get_stream(session_id)
        if not stream:
            emit('error', {'message': 'Stream not found'})
            return

        is_teacher = (user_role in ['faculty', 'kota_teacher'] or user_id == stream.get('teacher_id'))
        token = livekit_manager.generate_access_token(
            room_name=session_id,
            participant_identity=user_id,
            participant_name=user_name,
            is_teacher=is_teacher
        )
        if token:
            emit('livekit_token_generated', {
                'session_id': session_id,
                'livekit_url': LIVEKIT_URL,
                'livekit_token': token,
                'participant_id': user_id,
                'participant_name': user_name,
                'is_teacher': is_teacher
            })
        else:
            emit('error', {'message': 'Failed to generate LiveKit token'})
    except Exception as e:
        print(f"❌ Error generating LiveKit token: {e}")
        emit('error', {'message': 'Failed to generate token'})

@socketio.on('leave_enhanced_stream')
def handle_leave_enhanced_stream(data):
    try:
        session_id = data.get('session_id')
        if session_id:
            leave_room(session_id)
            enhanced_stream_manager.remove_viewer(session_id, request.sid)
            stream = enhanced_stream_manager.get_stream(session_id)
            if stream:
                emit('viewer_left', {
                    'viewer_count': stream['viewer_count']
                }, room=session_id)
    except Exception as e:
        print(f"❌ Error leaving enhanced stream: {e}")

@socketio.on('enhanced_video_frame')
def handle_enhanced_video_frame(data):
    try:
        session_id = data.get('session_id')
        frame_data = data.get('frame_data')
        quality = data.get('quality', 'medium')
        if not session_id or not frame_data:
            return
        if session_id in quality_settings:
            quality_settings[session_id]['video_quality'] = quality
        socketio.emit('enhanced_video_frame', {
            'session_id': session_id,
            'frame_data': frame_data,
            'quality': quality,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }, room=session_id, include_self=False)
    except Exception as e:
        print(f"❌ Error handling enhanced video frame: {e}")

@socketio.on('chat_message')
def handle_chat_message(data):
    try:
        session_id = data.get('session_id')
        message = data.get('message')
        sender_id = data.get('sender_id')
        sender_name = data.get('sender_name', 'Anonymous')
        if not session_id or not message:
            return
        sender_role = get_user_role_by_id(sender_id) or 'unknown'
        if session_id not in chat_messages:
            chat_messages[session_id] = []
        chat_data = {
            'id': str(uuid.uuid4()),
            'session_id': session_id,
            'sender_id': sender_id,
            'sender_name': sender_name,
            'sender_role': sender_role,
            'message': message,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        chat_messages[session_id].append(chat_data)
        socketio.emit('chat_message', chat_data, room=session_id)
        print(f"💬 Chat message from {sender_name} ({sender_role}) in session {session_id}: {message}")
    except Exception as e:
        print(f"❌ Error handling chat message: {e}")

@socketio.on('get_chat_history')
def handle_get_chat_history(data):
    try:
        session_id = data.get('session_id')
        if session_id and session_id in chat_messages:
            history = chat_messages[session_id][-50:]
            emit('chat_history', {
                'session_id': session_id,
                'messages': history
            })
        else:
            emit('chat_history', {
                'session_id': session_id,
                'messages': []
            })
    except Exception as e:
        print(f"❌ Error getting chat history: {e}")

@socketio.on('video_frame')
def handle_video_frame_legacy(data):
    try:
        session_id = data.get('session_id')
        frame_data = data.get('frame_data')
        frame_type = data.get('frame_type', 'camera')
        if not session_id or not frame_data:
            return
        socketio.emit('video_frame', {
            'session_id': session_id,
            'frame_data': frame_data,
            'frame_type': frame_type,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }, room=session_id, include_self=False)
    except Exception as e:
        print(f"❌ Error handling video frame: {e}")

@socketio.on('screen_frame')
def handle_screen_frame_legacy(data):
    try:
        session_id = data.get('session_id')
        frame_data = data.get('frame_data')
        if not session_id or not frame_data:
            return
        socketio.emit('screen_frame', {
            'session_id': session_id,
            'frame_data': frame_data,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }, room=session_id, include_self=False)
    except Exception as e:
        print(f"❌ Error handling screen frame: {e}")

if __name__ == '__main__':
    print("🚀 Starting Enhanced Real-time Streaming Service with LiveKit...")
    print("🎯 Features: LiveKit + WebRTC + Socket.IO + Chat + Recording + Quality Controls")
    print("🌐 CORS enabled for all origins with full preflight support")
    print("🔧 Socket.IO CORS configured with credentials support")
    print(f"🎥 LiveKit URL: {LIVEKIT_URL}")
    print(f"🔑 LiveKit API Key: {LIVEKIT_API_KEY}")
    print("🚀 Server starting on port 8012...")
    print("=" * 70)

    if not all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]):
        print("❌ LiveKit configuration missing! Please check your .env file.")
        print("Required: LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET")
        exit(1)

    print("✅ LiveKit configuration validated")
    print("🎬 Ready to create streaming rooms with LiveKit integration")
    DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() in ['true', '1', 't']

    # For production deployment, use allow_unsafe_werkzeug=True
    # Note: For better production performance, consider using Gunicorn
    socketio.run(app, host='0.0.0.0', port=8012, debug=DEBUG, allow_unsafe_werkzeug=True)